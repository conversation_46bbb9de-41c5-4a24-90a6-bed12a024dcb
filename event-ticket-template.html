<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="color-scheme" content="light dark">
  <title>Event Ticket</title>
  <style>
    /* Reset and base styles */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    @font-face {
            font-family: 'Lato';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/lato/v24/S6uyw4BMUTPHjx4wXg.woff2) format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        @font-face {
            font-family: 'Montserrat';
            font-style: normal;
            font-weight: 400;
            src: url(https://fonts.gstatic.com/s/montserrat/v26/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCu173w5aXo.woff2) format('woff2');
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

    body {
      font-family: Arial, Helvetica, sans-serif;
      background-color: #ffffff;
      padding: 20px;
      color: #333;
    }

    /* Container styles */
    .container {
      max-width: 800px;
      margin: 0 auto;
    }

    /* Ticket styles */
    .ticket {
      background-image: url('images/event-ticket-holder.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 10px;
      /* box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1); */
      position: relative;
      overflow: hidden;
      margin-bottom: 30px;
    }

    /* Ticket content */
    .ticket-content {
      display: flex;
      position: relative;
      padding: 20px 30px;
    }

    /* Left column - Ticket details */
    .ticket-details {
      flex: 1;
      padding: 18px;
      max-width: 460px;
    }

    .event-title {
      font-size: 36px;
      line-height: 32px;
      font-weight: bold;
      margin-bottom: 5px;
      font-family: 'Times New Roman', Times, serif;
    }

    .event-subtitle {
      color: #777;
      letter-spacing: 20px;
      margin-bottom: 25px;
      font-size: 24px;
      line-height: 30px;
    }

    .ticket-info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    .info-label {
      color: #777;
      font-size: 14px;
      margin-bottom: 5px;
    }

    .info-value {
      font-weight: bold;
    }


    /* Right column - QR code */
    .qr-section {
      padding: 20px 55px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-width: 220px;
    }

    .qr-instruction {
      text-align: center;
      font-size: 14px;
      color: #777;
      margin-bottom: 15px;
    }

    .qr-container {
      border: 1px solid #eee;
      padding: 10px;
      border-radius: 5px;
    }

    .qr-code {
      width: 150px;
      height: 150px;
      background-color: #f9f9f9;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #999;
      text-align: center;
    }

    /* Disclaimer section */
    .disclaimer-section {
      border-top: 1px solid #ddd;
      padding-top: 20px;
    }

    .disclaimer-label {
      color: #777;
      text-transform: uppercase;
      font-size: 12px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .disclaimer-text {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .terms-link {
      color: #ff9800;
      text-decoration: none;
    }

    /* Footer */
    .footer {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #666;
      border-top: 1px solid #eee;
      padding-top: 20px;
      margin-top: 20px;
    }

    .footer-text {
      flex: 1;
      font-style: italic;
    }

    .footer-logo {
      margin-left: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .footer-logo img {
      margin-right: 5px; /* Add spacing between image and text */
    }

    .footer-logo span {
      font-family: 'Montserrat', Arial, Helvetica, sans-serif;
      font-size: 16px;
      color: #333; /* Dark text for light mode */
    }

    /* Scroll indicator styles */
    .scroll-indicator {
      display: none;
      text-align: center;
      color: #777;
      font-size: 14px;
      margin-bottom: 10px;
      animation: fadeInOut 2s infinite;
    }

    @keyframes fadeInOut {
      0%, 100% { opacity: 0.5; }
      50% { opacity: 1; }
    }

    /* Responsive styles */
    @media (max-width: 700px) {
      .scroll-indicator {
        display: block;
      }

      .ticket-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 30px;
        position: relative;
      }

      .ticket {
        background-image: url('images/event-ticket-holder.png');
        background-size: cover;
        background-position: center;
        min-width: 700px; /* Ensure minimum width to prevent content squishing */
        margin-bottom: 0;
      }

      /* Keep the horizontal layout for the ticket content */
      .ticket-content {
        flex-direction: row;
      }

      .ticket-details {
        max-width: 460px; /* Keep original max-width */
      }

      .qr-section {
        padding: 20px 30px;
      }
    }

    /* Dark mode styles */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #121212;
        color: #f5f5f5;
      }

      .ticket {
        /* Keep the original background image in dark mode */
        filter: brightness(0.9);
      }

      /* Keep ticket text dark since the background image is white */
      .ticket-details {
        color: #333; /* Keep text dark */
      }

      .event-title {
        color: #000; /* Keep title dark */
      }

      .event-subtitle {
        color: #777; /* Keep subtitle as in light mode */
      }

      .info-label {
        color: #777; /* Keep labels as in light mode */
      }

      .info-value {
        color: #333; /* Keep values dark */
      }

      .qr-instruction {
        color: #777; /* Keep QR instructions as in light mode */
      }

      /* Only apply dark mode styles to elements outside the ticket */
      .disclaimer-label, .scroll-indicator {
        color: #aaa;
      }

      .qr-code {
        background-color: #333;
        color: #ddd;
      }

      .footer {
        color: #bbb;
        border-top-color: #444;
      }

      .footer-logo span {
        color: #f5f5f5; /* Light text for dark mode */
      }

      .disclaimer-section {
        border-top-color: #444;
      }

      /* Ensure notches match dark background */
      .notch-top, .notch-bottom {
        background-color: #333;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Scroll indicator for mobile -->
    <div class="scroll-indicator">← Swipe to view full ticket →</div>

    <!-- Ticket Wrapper for horizontal scrolling on mobile -->
    <div class="ticket-wrapper">
      <!-- Ticket -->
      <div class="ticket">
        <!-- Ticket Content -->
        <div class="ticket-content">
          <!-- Left Column - Ticket Details -->
          <div class="ticket-details">
            <h1 class="event-title">Startup Weekend</h1>
            <p class="event-subtitle">Event Ticket</p>

            <div class="ticket-info-grid">
              <div class="ticket-info-item">
                <p class="info-label">Ticket number</p>
                <p class="info-value">12345678</p>
              </div>

              <div class="ticket-info-item">
                <p class="info-label">Ticket type</p>
                <p class="info-value">VVIP Golden</p>
              </div>

              <div class="ticket-info-item">
                <p class="info-label">Ticket amount</p>
                <p class="info-value">$2,000</p>
              </div>

              <div class="ticket-info-item">
                <p class="info-label">Attendee details</p>
                <p class="info-value">Gael Gadah</p>
              </div>

              <div class="ticket-info-item">
                <p class="info-label">Location</p>
                <p class="info-value">Cleveland Heights</p>
              </div>

              <div class="ticket-info-item">
                <p class="info-label">Date & Time</p>
                <p class="info-value">20th Apr, 2025 • 6:00EST</p>
              </div>
            </div>
          </div>

          <!-- Right Column - QR Code -->
          <div class="qr-section">
            <p class="qr-instruction">
              To be scanned by<br>
              event organizers only
            </p>
            <div class="qr-container">
              <!-- Replace with actual QR code image for production -->
              <img src="https://static.wixstatic.com/media/12e4d0_dd5a51e84bd147a1857eb4fd7c19a51a~mv2.png" style="width: 150px; height: 150px;">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Disclaimer Section -->
    <div class="disclaimer-section">
      <p class="disclaimer-label">DISCLAIMER</p>
      <p class="disclaimer-text">
        Each ticket can only be used ONCE so do not share your QR with anyone.
      </p>
      <p>
        View [Event Name]'s
        <a href="#" class="terms-link">terms and conditions here</a>
      </p>

      <div class="footer">
        <div class="footer-text">
          <p>Throwing an event? Get the party started in no time with Maoney Events!</p>
          <p>Sell tickets, spread the word, and vibe with your community — all in minutes</p>
          <a href="http://www.maoney.com" class="terms-link">www.maoney.com</a>
        </div>
        <div class="footer-logo">
          <img src="https://maoneycontent.s3.us-east-2.amazonaws.com/email_template/maoney-main.png" alt="Maoney" width="24" height="24" style="display: block;">
          <span>Maoney</span>
        </div>
      </div>
    </div>
  </div>
</body>
</html>